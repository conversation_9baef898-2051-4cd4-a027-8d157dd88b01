#!/usr/bin/env python3

import socket
import re
from Crypto.Random.random import randint, choice

# The limit from the challenge
limit = 0xe5db6a6d765b1ba6e727aa7a87a792c49bb9ddeb2bad999f5ea04f047255d5a72e193a7d58aa8ef619b0262de6d25651085842fd9c385fa4f1032c305f44b8a4f92b16c8115d0595cebfccc1c655ca20db597ff1f01e0db70b9073fbaa1ae5e489484c7a45c215ea02db3c77f1865e1e8597cb0b0af3241cd8214bd5b5c1491f

def walking(x, y, part):
    """Transform coordinates using an 8-byte part"""
    epart = [int.from_bytes(part[i:i+2], "big") for i in range(0, len(part), 2)]
    xx = epart[0] * x + epart[1] * y
    yy = epart[2] * x + epart[3] * y
    return xx, yy

def solve_cat_position(start_pos, end_pos, mind_hex):
    """
    Try to find the sequence of mind parts that transforms start_pos to end_pos
    """
    mind = bytes.fromhex(mind_hex)
    parts = [mind[i:i+8] for i in range(0, len(mind), 8)]

    print(f"Start position: {start_pos}")
    print(f"End position: {end_pos}")
    print(f"Number of mind parts: {len(parts)}")

    # Let's try a more targeted approach
    # Since the search space is huge, let's try some heuristics

    # First, try single steps
    for part in parts:
        new_pos = walking(start_pos[0], start_pos[1], part)
        if new_pos[0] > limit or new_pos[1] > limit:
            new_pos = (new_pos[0] % limit, new_pos[1] % limit)
        if new_pos == end_pos:
            print("Found single-step solution!")
            return part

    # Try two steps
    print("Trying two-step solutions...")
    for i, part1 in enumerate(parts):
        pos1 = walking(start_pos[0], start_pos[1], part1)
        if pos1[0] > limit or pos1[1] > limit:
            pos1 = (pos1[0] % limit, pos1[1] % limit)
            if pos1 == end_pos:
                return part1
            continue

        for j, part2 in enumerate(parts):
            if i == j:
                continue
            pos2 = walking(pos1[0], pos1[1], part2)
            if pos2[0] > limit or pos2[1] > limit:
                pos2 = (pos2[0] % limit, pos2[1] % limit)
            if pos2 == end_pos:
                print("Found two-step solution!")
                return part1 + part2

    # Try three steps (this might be slow)
    print("Trying three-step solutions...")
    count = 0
    for i, part1 in enumerate(parts):
        pos1 = walking(start_pos[0], start_pos[1], part1)
        if pos1[0] > limit or pos1[1] > limit:
            pos1 = (pos1[0] % limit, pos1[1] % limit)
            if pos1 == end_pos:
                return part1
            continue

        for j, part2 in enumerate(parts):
            if i == j:
                continue
            pos2 = walking(pos1[0], pos1[1], part2)
            if pos2[0] > limit or pos2[1] > limit:
                pos2 = (pos2[0] % limit, pos2[1] % limit)
                if pos2 == end_pos:
                    return part1 + part2
                continue

            for k, part3 in enumerate(parts):
                if k == i or k == j:
                    continue
                count += 1
                if count % 10000 == 0:
                    print(f"Tried {count} three-step combinations...")

                pos3 = walking(pos2[0], pos2[1], part3)
                if pos3[0] > limit or pos3[1] > limit:
                    pos3 = (pos3[0] % limit, pos3[1] % limit)
                if pos3 == end_pos:
                    print("Found three-step solution!")
                    return part1 + part2 + part3

    print("No solution found in 1-3 steps")
    return None

def connect_and_solve():
    """Connect to the challenge server and solve each round"""
    
    host = "catch.chal.idek.team"
    port = 1337
    
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.connect((host, port))
    
    def recv_until(pattern):
        data = b""
        while True:
            chunk = sock.recv(1024)
            if not chunk:
                break
            data += chunk
            if pattern in data:
                break
        return data.decode('utf-8', errors='ignore')
    
    def send_line(line):
        sock.send((line + '\n').encode())
    
    try:
        for round_num in range(20):
            print(f"\n=== Round {round_num + 1}/20 ===")
            
            # Read the round start
            data = recv_until(b"Co-location:")
            print("Received:", data)
            
            # Extract starting position
            start_match = re.search(r"Co-location: \((\d+), (\d+)\)", data)
            if not start_match:
                print("Failed to parse starting position")
                break
            
            start_pos = (int(start_match.group(1)), int(start_match.group(2)))
            
            # Extract mind - need to get all the hex data across multiple lines
            mind_start = data.find("Cat's hidden mind: ")
            if mind_start == -1:
                print("Failed to find mind start")
                break

            mind_data = data[mind_start + len("Cat's hidden mind: "):]
            # Read until we get to the next emoji or message
            mind_end = mind_data.find("😸")
            if mind_end == -1:
                mind_end = mind_data.find("The chase")
            if mind_end == -1:
                print("Failed to find mind end")
                break

            mind_hex = mind_data[:mind_end].strip().replace('\n', '').replace(' ', '')
            
            # Wait for final position
            data = recv_until(b"Cat now at:")
            print("Received:", data)
            
            # Extract final position
            end_match = re.search(r"Cat now at: \((\d+), (\d+)\)", data)
            if not end_match:
                print("Failed to parse final position")
                break
            
            end_pos = (int(end_match.group(1)), int(end_match.group(2)))
            
            # Solve for the path
            solution = solve_cat_position(start_pos, end_pos, mind_hex)
            
            if solution is None:
                print("Failed to find solution!")
                break
            
            # Send the solution
            solution_hex = solution.hex()
            print(f"Sending solution: {solution_hex}")
            send_line(solution_hex)
            
            # Read response
            data = recv_until(b"friend!")
            print("Response:", data)
            
            if "Lost in the labyrinth" in data or "path eludes you" in data:
                print("Solution was incorrect!")
                break
        
        # Try to get the flag
        data = recv_until(b"Victory!")
        print("Final response:", data)
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        sock.close()

if __name__ == "__main__":
    connect_and_solve()
