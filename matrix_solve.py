#!/usr/bin/env python3

import numpy as np
from sympy import Matrix, mod_inverse
from sympy.abc import x, y

# The limit from the challenge
limit = 0xe5db6a6d765b1ba6e727aa7a87a792c49bb9ddeb2bad999f5ea04f047255d5a72e193a7d58aa8ef619b0262de6d25651085842fd9c385fa4f1032c305f44b8a4f92b16c8115d0595cebfccc1c655ca20db597ff1f01e0db70b9073fbaa1ae5e489484c7a45c215ea02db3c77f1865e1e8597cb0b0af3241cd8214bd5b5c1491f

def walking(x, y, part):
    """Transform coordinates using an 8-byte part"""
    epart = [int.from_bytes(part[i:i+2], "big") for i in range(0, len(part), 2)]
    xx = epart[0] * x + epart[1] * y
    yy = epart[2] * x + epart[3] * y
    return xx, yy

def part_to_matrix(part):
    """Convert an 8-byte part to a 2x2 transformation matrix"""
    epart = [int.from_bytes(part[i:i+2], "big") for i in range(0, len(part), 2)]
    return Matrix([[epart[0], epart[1]], [epart[2], epart[3]]])

# Test data
start_pos = (30419881756223004241970033588873759182826779568887661583210949159704720033726, 111112490405250854961944300837965843920899115716721515315522311817194401311735)
end_pos = (30270495266906033395152960938644827359267925848344885571530408767526569242890140326025442192441431793982909896191239766173546390117896823736397411992666784005519196401899245932104031107528685356639379025662685627019132928, 45272068792794423107868998975115058100249117381839936661489581749113375796725058162864264263382157435124057578054435858514424978645585485801961645403189485127422951469205015250943441807090122961826941444782955291186054656)

mind_hex = "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"

mind = bytes.fromhex(mind_hex)
parts = [mind[i:i+8] for i in range(0, len(mind), 8)]

print(f"Number of parts: {len(parts)}")

# Convert parts to matrices
matrices = [part_to_matrix(part) for part in parts]

print("First few matrices:")
for i in range(min(5, len(matrices))):
    print(f"Matrix {i}:")
    print(matrices[i])
    print()

# Try to find single matrix that transforms start to end
start_vec = Matrix([start_pos[0], start_pos[1]])
end_vec = Matrix([end_pos[0], end_pos[1]])

print(f"Start vector: {start_vec}")
print(f"End vector: {end_vec}")

# For single step: M * start = end, so M = end * start^(-1)
# But start is a vector, not a matrix, so this doesn't work directly

# Let's try a different approach - brute force with matrices
print("\nTrying single matrix transformations...")
for i, matrix in enumerate(matrices):
    result = matrix * start_vec
    if result[0] == end_vec[0] and result[1] == end_vec[1]:
        print(f"Found single-step solution with matrix {i}!")
        print(f"Part: {parts[i].hex()}")
        break
else:
    print("No single-step solution found")

# Try two-step combinations (limited)
print("\nTrying two-step matrix combinations (first 20)...")
for i in range(min(20, len(matrices))):
    for j in range(min(20, len(matrices))):
        if i == j:
            continue
        
        combined_matrix = matrices[j] * matrices[i]  # Note: order matters, j then i
        result = combined_matrix * start_vec
        
        if result[0] == end_vec[0] and result[1] == end_vec[1]:
            print(f"Found two-step solution with matrices {i}, {j}!")
            print(f"Parts: {parts[i].hex()}{parts[j].hex()}")
            break
    else:
        continue
    break
else:
    print("No two-step solution found in first 20x20")
