#!/usr/bin/env python3

from pwn import remote

# Challenge server
HOST = "catch.chal.idek.team"
PORT = 1337

# Large modulus limit
limit = int("e5db6a6d765b1ba6e727aa7a87a792c49bb9ddeb2bad999f5ea04f047255d5a72e193a7d58aa8ef619b0262de6d25651085842fd9c385fa4f1032c305f44b8a4f92b16c8115d0595cebfccc1c655ca20db597ff1f01e0db70b9073fbaa1ae5e489484c7a45c215ea02db3c77f1865e1e8597cb0b0af3241cd8214bd5b5c1491f", 16)

def walking(x, y, part):
    epart = [int.from_bytes(part[i:i+2], "big") for i in range(0, len(part), 2)]
    return epart[0]*x + epart[1]*y, epart[2]*x + epart[3]*y

def mod_if_needed(x, y):
    if x > limit or y > limit:
        x %= limit
        y %= limit
    return x, y

def solve_cat(x0, y0, xf, yf, steps):
    print(f"Solving: ({x0}, {y0}) -> ({xf}, {yf}) with {len(steps)} steps")

    # Check if the end position suggests modulo was applied
    end_has_modulo = (xf < x0 and yf < y0) or str(xf).endswith('0000') or str(yf).endswith('0000')
    print(f"End position suggests modulo applied: {end_has_modulo}")

    # Step 1: 1-step search (most cats stop after 1 move)
    print("Trying 1-step solutions...")
    for i, part in enumerate(steps):
        x1, y1 = walking(x0, y0, part)
        # Check if this step would cause modulo
        if x1 > limit or y1 > limit:
            x1, y1 = x1 % limit, y1 % limit
            if (x1, y1) == (xf, yf):
                print(f"Found 1-step solution with part {i} (hit limit)")
                return part
        elif (x1, y1) == (xf, yf):
            print(f"Found 1-step solution with part {i}")
            return part

    # Step 2: 2-step search
    print("Trying 2-step solutions...")
    for i, p1 in enumerate(steps):
        x1, y1 = walking(x0, y0, p1)
        hit_limit_1 = x1 > limit or y1 > limit
        if hit_limit_1:
            x1, y1 = x1 % limit, y1 % limit
            # If we hit limit on first step, cat stops here
            if (x1, y1) == (xf, yf):
                return p1
            continue  # Cat stops, can't take second step

        for j, p2 in enumerate(steps):
            if j == i:
                continue
            x2, y2 = walking(x1, y1, p2)
            if x2 > limit or y2 > limit:
                x2, y2 = x2 % limit, y2 % limit
            if (x2, y2) == (xf, yf):
                print(f"Found 2-step solution with parts {i}, {j}")
                return p1 + p2

    # Step 3: 3-step search (limited to avoid timeout)
    print("Trying 3-step solutions (limited search)...")
    count = 0
    max_3_step = 50000  # Limit to avoid timeout

    for i, p1 in enumerate(steps):
        x1, y1 = walking(x0, y0, p1)
        if x1 > limit or y1 > limit:
            x1, y1 = x1 % limit, y1 % limit
            if (x1, y1) == (xf, yf):
                return p1
            continue

        for j, p2 in enumerate(steps):
            if j == i:
                continue
            x2, y2 = walking(x1, y1, p2)
            if x2 > limit or y2 > limit:
                x2, y2 = x2 % limit, y2 % limit
                if (x2, y2) == (xf, yf):
                    return p1 + p2
                continue

            for k, p3 in enumerate(steps):
                if k in (i, j):
                    continue
                count += 1
                if count > max_3_step:
                    print(f"Reached 3-step limit ({max_3_step})")
                    break

                x3, y3 = walking(x2, y2, p3)
                if x3 > limit or y3 > limit:
                    x3, y3 = x3 % limit, y3 % limit
                if (x3, y3) == (xf, yf):
                    print(f"Found 3-step solution with parts {i}, {j}, {k}")
                    return p1 + p2 + p3
            else:
                continue
            break
        else:
            continue
        break

    print("No solution found in limited search")
    return None

def main():
    # Connect to the challenge
    io = remote(HOST, PORT)

    try:
        for round_idx in range(20):
            print(f"\n=== Round {round_idx + 1}/20 ===")
            
            # Parse starting position
            io.recvuntil(b"Co-location: (")
            x0 = int(io.recvuntil(b",")[:-1])
            y0 = int(io.recvuntil(b")")[:-1])
            print(f"Start position: ({x0}, {y0})")

            # Parse mind
            io.recvuntil(b"Cat's hidden mind: ")
            mind_hex = io.recvline().strip().decode()
            mind = bytes.fromhex(mind_hex)
            steps = [mind[i:i+8] for i in range(0, len(mind), 8)]
            print(f"Mind has {len(steps)} parts")

            # Parse final position
            io.recvuntil(b"Cat now at: (")
            xf = int(io.recvuntil(b",")[:-1])
            yf = int(io.recvuntil(b")")[:-1])
            print(f"End position: ({xf}, {yf})")

            # Solve for the path
            path = solve_cat(x0, y0, xf, yf, steps)
            if path is None:
                print("❌ No path found for this cat!")
                io.close()
                return

            # Send solution
            solution_hex = path.hex()
            print(f"Sending solution: {solution_hex}")
            io.sendline(solution_hex.encode())
            
            # Check response
            response = io.recvline().decode()
            print(f"Response: {response.strip()}")
            
            if "Lost in the labyrinth" in response or "path eludes you" in response:
                print("❌ Solution was incorrect!")
                io.close()
                return

        # Receive final flag
        print("\n🎉 All rounds completed! Getting flag...")
        flag_response = io.recvall().decode()
        print(flag_response)
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        io.close()

if __name__ == "__main__":
    main()
